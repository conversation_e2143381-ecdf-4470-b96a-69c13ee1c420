import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import colors from '../Utils/colors';
import {fp, hp, wp} from '../Helper/ResponsiveDimensions';
import icons from '../Utils/icons';
import {Fonts} from '../Utils/Fonts';
import {responsiveFontSize} from '../Utils/constant';
import {PrimaryButton} from './CustomButton';
import {
  convertTo12HourFormat,
  convertToDMY,
  convertToLocal12HourFormat,
  convertToLocal24HourFormat,
  isShowJoinButton,
} from '../Helper/DateHelpers/DateHelpers';
import {useNavigation} from '@react-navigation/native';
import {useSelector} from 'react-redux';
import moment from 'moment';
import {useTranslation} from 'react-i18next';

const Accordian = ({content, item, onPressJoin, classType = '', type = ''}) => {
  console.log('🚀 ~ type:', type);
  console.log('🚀 ~ Accordian ~ class_type:', classType);
  console.log('🚀 ~ Accordian ~ item:', item);
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const [open, setOpen] = useState(false);
  const [showButton, setShowButton] = useState(false);
  const now = new Date();

  useEffect(() => {
    // Define the check visibility function inside useEffect to access latest props/state
    const checkButtonVisibility = () => {
      const {start_time, end_time} = item?.tlm_tutor_schedule;

      const isButtonDisable = isShowJoinButton(
        item?.date,
        convertToLocal24HourFormat(start_time),
        convertToLocal24HourFormat(end_time),
      );
      if (classType == 'Face to Face') {
        setShowButton(false);
      } else {
        setShowButton(!isButtonDisable);
      }
    };

    // Initial check
    checkButtonVisibility();

    // Set up interval for periodic checks
    const interval = setInterval(checkButtonVisibility, 60000); // Check every minute

    // Cleanup interval on component unmount
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [item]); // Add item as dependency since we're using it in the effect

  const {user_type} = useSelector(state => state?.auth);
  const renderTime = () => {
    const {start_time, end_time} = item?.tlm_tutor_schedule;
    const startTime = `${convertToLocal12HourFormat(start_time)}`;
    const endTime = convertToLocal12HourFormat(end_time);
    return isRTL ? `${endTime} - ${startTime}` : `${startTime} - ${endTime}`;
  };
  console.log(
    '🚀 ~ Accordian ~ convertToLocal12HourFormat: START',
    convertToLocal12HourFormat(item?.tlm_tutor_schedule?.start_time),
    '-',
  );
  console.log(
    '🚀 ~ Accordian ~ convertToLocal12HourFormat: END',
    convertToLocal12HourFormat(item?.tlm_tutor_schedule?.end_time),
  );
  return (
    <TouchableOpacity
      style={styles.accordian}
      activeOpacity={0.8}
      onPress={() => setOpen(!open)}>
      <View
        style={[styles.header, {flexDirection: isRTL ? 'row-reverse' : 'row'}]}>
        <Text style={styles.date}>
          {moment(item?.date).format('DD MMM YYYY')}
          {/* {convertToDMY(item?.date || item?.specific_date)} */}
        </Text>

        {/* <Image
          source={open ? icons.upArrow : icons.downArrowBlack}
          style={styles.arrow}
        /> */}
      </View>

      {/* {open && ( */}
      <View style={styles.content}>
        <View
          style={{
            flexDirection: isRTL ? 'row-reverse' : 'row',
            justifyContent: 'space-between',
          }}>
          {/* <Text
            style={[styles.time, {textAlign: isRTL ? 'right' : 'left'}]}>{`${t(
            'time',
          )} : `}</Text> */}
          <Text style={[styles.timeTxt, {textAlign: isRTL ? 'right' : 'left'}]}>
            {t('time')} : {renderTime()}
          </Text>

          {showButton ? (
            user_type == '1' || user_type == '2' ? (
              <PrimaryButton
                title={
                  type == 'openSession' ||
                  (type != 'openSession' && item?.status == '1')
                    ? t('Join')
                    : moment(item?.end_time, 'HH:mm:ss').isAfter(moment())
                    ? t('Review')
                    : t('reschedule')
                }
                onPress={onPressJoin}
                style={{width: wp(30), height: fp(4)}}
                textStyle={{
                  fontSize: responsiveFontSize(16),
                  fontFamily: Fonts.semiBold,
                }}
              />
            ) : (
              <PrimaryButton
                title={t('start_class')}
                onPress={onPressJoin}
                style={{width: wp(30), height: fp(4)}}
                textStyle={{
                  fontSize: responsiveFontSize(16),
                  fontFamily: Fonts.semiBold,
                }}
              />
            )
          ) : null}

          {/*
          <PrimaryButton
            title={
              type == 'openSession' ||
              (type != 'openSession' && item?.status == '1')
                ? t('Join')
                : moment(item?.end_time, 'HH:mm:ss').isAfter(moment())
                ? t('Review')
                : t('reschedule')
            }
            onPress={onPressJoin}
            style={{width: wp(30), height: fp(4)}}
            textStyle={{
              fontSize: responsiveFontSize(16),
              fontFamily: Fonts.semiBold,
            }}
          />
        </View>
        <View style={styles.bottom}>
          {/* <Text style={styles.time}>
              {'Class Link : '}
              <Text style={styles.timeTxt}>{item?.class_link}</Text>
            </Text> */}
        </View>
      </View>
      {/* )} */}
    </TouchableOpacity>
  );
};

export default Accordian;

const styles = StyleSheet.create({
  accordian: {
    padding: 8,
    borderWidth: 1,
    borderColor: colors.txtGrey,
    marginVertical: hp(1),
    borderRadius: 8,
  },
  header: {
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  date: {
    fontFamily: Fonts.medium,
    fontSize: responsiveFontSize(14),
    color: colors.searchGray,
  },
  arrow: {
    width: 24,
    height: 24,
  },
  content: {
    marginVertical: 5,
  },
  time: {
    fontFamily: Fonts.medium,
    color: colors.txtGrey1,
    fontSize: fp(1.6),
  },
  timeTxt: {
    fontFamily: Fonts.medium,
    color: colors.darkBlack,
    fontSize: fp(1.6),
  },
  bottom: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});
