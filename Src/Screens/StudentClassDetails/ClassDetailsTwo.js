import {
  Image,
  Modal,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import colors from '../../Utils/colors';
import {AppHeader} from '../../Components/Header';
import icons from '../../Utils/icons';
import {useTranslation} from 'react-i18next';
import LinearGradient from 'react-native-linear-gradient';
import {styles} from './styles';
import {fp, hp, wp} from '../../Helper/ResponsiveDimensions';
import {PrimaryButton} from '../../Components/CustomButton';
import {
  useGetOpenSessionScheduleDetailsByIdQuery,
  useGetSudendtBookingDetailsByIdQuery,
  useGetTutorBookingDetailsByIdQuery,
  useGetUpcommingScheduleDetailsQuery,
  useGetUpcommingTutorScheduleDetailsQuery,
  useLazyStartClassTutorQuery,
  useTutorBookingCancelMutation,
} from '../../Api/ApiSlice';
import {
  convertTo12HourFormat,
  convertToDMY,
  convertToLocal12HourFormat,
} from '../../Helper/DateHelpers/DateHelpers';
import {IMAGE_BASE_URL} from '../../Utils/getBaseUrl';
import {DUMMY_USER_IMG, responsiveFontSize} from '../../Utils/constant';
import {useDispatch, useSelector} from 'react-redux';
import moment from 'moment';
import {Fonts} from '../../Utils/Fonts';
import {showToast} from '../../Components/ToastHelper';
import {
  updateClassType,
  updateGroupStudents,
  updateMeetingTutorPoint,
  updatePackage,
  updateSlot,
} from '../../Redux/Slices/Student/TutorBookingSlice';
import {
  resetBookingSchedules,
  resetSelectedSlots,
  updateSelectedSlots,
} from '../../Redux/Slices/Student/SlotSlice';
import TaleemLoader from '../../Components/TaleemLoader/TaleemLoader';
import {useFocusEffect} from '@react-navigation/native';

const ClassDetailsTwo = ({navigation, route}) => {
  const {t, i18n} = useTranslation();
  const isRTL = i18n.language === 'ar';
  const {item, tutor, bookingDetails, lastItemId, data1, type} =
    route?.params || {};
  console.log('🚀 ~ ClassDetailsTwo ~ item:', item);
  const [data, setData] = useState();
  const [reason, setReason] = useState('');
  const [isLogoutModalVisible, setIsLogoutModalVisible] = useState(false);
  const dispatch = useDispatch();
  const {user_type} = useSelector(state => state?.auth);

  const {data: upcommingClass} = useGetUpcommingScheduleDetailsQuery(
    item?.booking_id,
  );
  const {data: upcommingTutorClass} = useGetUpcommingTutorScheduleDetailsQuery(
    item?.booking_id,
  );
  console.log('🚀 ~ ClassDetailsTwo ~ item?.id:', item?.id);
  const [
    cancelTutorBooking,
    {data: cancelTutorBookingRes, isLoading: cancelTutorBookingLoading},
  ] = useTutorBookingCancelMutation();

  const [
    startClassTutorApi,
    {data: startClassTutorRes, isLoading: startClassTutorLoading},
  ] = useLazyStartClassTutorQuery();

  const {
    data: generalScheduleDetails,
    refetch: refetchStudentBookingDetails,
    isLoading: isStudentResLoading,
  } = useGetSudendtBookingDetailsByIdQuery({
    id: item?.id,
    user_type: user_type == '3' ? 'tutor' : 'student',
  });
  console.log('🚀 ~ ClassDetailsTwo ~ item?.id:', item?.id);

  const {
    data: openSessionScheduleDetailsData,
    refetch: refetchOpenSessionScheduleDetails,
    isLoading: isOpenSessionScheduleDetailsLoading,
  } = useGetOpenSessionScheduleDetailsByIdQuery({
    id: item?.id,
    user_type: user_type == '3' ? 'tutor' : 'student',
  });

  const scheduleDetailsData =
    generalScheduleDetails || openSessionScheduleDetailsData;

  useFocusEffect(
    React.useCallback(() => {
      refetchStudentBookingDetails();
      refetchOpenSessionScheduleDetails();
    }, [refetchStudentBookingDetails, refetchOpenSessionScheduleDetails]),
  );

  useEffect(() => {
    console.log(
      'scheduleDetailsData====',
      scheduleDetailsData?.data?.bookingSchedule,
    );
  }, [scheduleDetailsData]);

  const handleCancelTutorBooking = () => {
    if (reason == '') {
      showToast('error', t('specifyCancelReason'), 'bottom', isRTL);
      return;
    }
    const payload = {
      schedule_id: item?.id,
      cancellation_reason: reason,
    };
    cancelTutorBooking(payload)
      .unwrap()
      .then(response => {
        handleCancelLogout();
        navigation.navigate('My Class');
        showToast('success', response?.message), 'bottom', isRTL;
      })

      .catch(err => {
        console.error('Error booking Cancel:', err);

        showToast('error', err?.data?.message, 'bottom', isRTL);
      });
  };
  const handleCancelLogout = () => {
    // Hide logout confirmation modal
    setIsLogoutModalVisible(false);
  };
  const handleLogoutConfirmation = () => {
    // Show logout confirmation modal
    setIsLogoutModalVisible(true);
  };
  useEffect(() => {
    if (user_type == '1') setData(upcommingClass?.data?.bookingSchedule);
    else setData(upcommingTutorClass?.data?.bookingSchedule);
  }, [upcommingTutorClass, upcommingClass]);
  function checkLoadingStates(
    type,
    isOpenSessionScheduleDetailsLoading,
    isStudentResLoading,
    isRTL,
  ) {
    if (type === 'openSession' && isOpenSessionScheduleDetailsLoading) {
      console.log(
        '🚀 ~ checkLoadingStates ~ isOpenSessionScheduleDetailsLoading:',
        isOpenSessionScheduleDetailsLoading,
      );
      showToast(
        'info',
        'Loading class details, please wait...',
        'bottom',
        isRTL,
      );
      return true;
    }
    if (isStudentResLoading) {
      console.log(
        '🚀 ~ checkLoadingStates ~ isStudentResLoading:',
        isStudentResLoading,
      );
      showToast(
        'info',
        'Loading class details, please wait...',
        'bottom',
        isRTL,
      );
      return true;
    }
    return false;
  }
  async function handleStartClassTutor() {
    try {
      const isLoading = checkLoadingStates(
        type,
        isOpenSessionScheduleDetailsLoading,
        isStudentResLoading,
        isRTL,
      );
      if (isLoading) return;
      const params = {
        id: item?.id,
        isOpenSession: type == 'openSession' ? 1 : 0,
      };
      console.log('🚀 ~ handleStartClassTutor ~ params:', params);
      const response = await startClassTutorApi(params).unwrap();
      console.log('🚀 ~ classDetailsTwo ~ response:', response);
      navigation.navigate('StartClassScreen', {
        lastItemId,
        id:
          scheduleDetailsData?.data?.bookingSchedule?.id ||
          openSessionScheduleDetailsData?.data?.tlm_open_session_schedules[0]
            ?.id,
        bookingId:
          bookingDetails?.id ||
          item?.booking_id ||
          openSessionScheduleDetailsData?.data?.id,
        tutor,
        agoraToken: response?.data?.roomToken,
        channelName: response?.data?.channelName,
        whiteBoardToken: response?.data?.whiteBoardToken,
        userName: response?.data?.userData?.name,
        whiteBoardUUID: response?.data?.whiteboard_link,
      });
    } catch (err) {
      console.error('Error Start Class classDetailsTwo:', err);
      // if (err?.data?.data?.wallet_status === 'insufficient') {
      //   navigation.navigate('Wallet');
      // }
      // if (err?.status == 409) {
      //   Alert.alert('Alert', err?.data?.message);
      // } else {
      showToast('error', err?.data?.message, 'bottom', isRTL);
      // }
    }
  }

  function checkClassStartConditions(
    type,
    isOpenSessionScheduleDetailsLoading,
    isStudentResLoading,
    scheduleDetailsData,
    isRTL,
  ) {
    if (type === 'openSession') {
      if (isOpenSessionScheduleDetailsLoading) {
        console.log(
          '🚀 ~ checkClassStartConditions ~ isOpenSessionScheduleDetailsLoading:',
          isOpenSessionScheduleDetailsLoading,
        );
        showToast(
          'info',
          'Loading class details, please wait...',
          'bottom',
          isRTL,
        );
        return false;
      }
      if (
        !scheduleDetailsData?.data?.tlm_open_session_schedules?.[0]
          ?.is_class_started
      ) {
        showToast(
          'error',
          'Your class has not been started by the tutor yet.',
          'bottom',
          isRTL,
        );
        return false;
      }
    } else if (isStudentResLoading) {
      console.log(
        '🚀 ~ checkClassStartConditions ~ isStudentResLoading:',
        isStudentResLoading,
      );
      showToast(
        'info',
        'Loading class details, please wait...',
        'bottom',
        isRTL,
      );
      return false;
    }
    return true;
  }

  function handleJoinClassStudent() {
    const canStart = checkClassStartConditions(
      type,
      isOpenSessionScheduleDetailsLoading,
      isStudentResLoading,
      scheduleDetailsData,
      isRTL,
    );
    if (!canStart) return;

    navigation.navigate('StartClassScreen', {
      lastItemId,
      id:
        scheduleDetailsData?.data?.bookingSchedule?.id ||
        openSessionScheduleDetailsData?.data?.tlm_open_session_schedules?.[0]
          ?.id,
      bookingId:
        bookingDetails?.id ||
        item?.booking_id ||
        openSessionScheduleDetailsData?.data?.id,
      tutor,
      agoraToken:
        scheduleDetailsData?.data?.bookingSchedule?.class_link ||
        openSessionScheduleDetailsData?.data?.tlm_open_session_schedules?.[0]
          ?.class_link,
      channelName:
        scheduleDetailsData?.data?.bookingSchedule?.channel_name ||
        openSessionScheduleDetailsData?.data?.tlm_open_session_schedules?.[0]
          ?.channel_name,
      whiteBoardToken:
        scheduleDetailsData?.data?.bookingSchedule?.whiteBoardToken ||
        openSessionScheduleDetailsData?.data?.tlm_open_session_schedules?.[0]
          ?.whiteBoardToken,
      whiteBoardUUID:
        scheduleDetailsData?.data?.bookingSchedule?.whiteboard_link ||
        openSessionScheduleDetailsData?.data?.tlm_open_session_schedules?.[0]
          ?.whiteboard_link,
      userName: 'Student',
      end_time:
        scheduleDetailsData?.data?.bookingSchedule?.end_time ||
        openSessionScheduleDetailsData?.data?.tlm_open_session_schedules?.[0]
          ?.end_time,
    });
  }

  return (
    <SafeAreaView style={styles.containerDetails}>
      <AppHeader
        backIcon={icons.backbtn}
        isWhite={true}
        isBackBtn
        title={t('classDetails')}
      />
      <ScrollView style={{flex: 1}}>
        <TaleemLoader
          isLoading={startClassTutorLoading || isStudentResLoading}
        />
        <LinearGradient
          colors={['#40A39B', '#C6FFC9']}
          start={{x: 0.0, y: 0.0}}
          end={{x: 1.0, y: 0.0}}
          style={styles.cardGradient}>
          <Text style={styles.details}>{t('meetingDetails')}</Text>
          <Text
            style={[
              styles.blackTxt,
              !isRTL && {width: wp(90)},
              {
                // width: wp(90),
                marginTop: 8,
                lineHeight: hp(2),
                textAlign: isRTL ? 'right' : 'left',
              },
            ]}>
            {scheduleDetailsData?.data?.bookingSchedule?.tlm_booking
              ?.class_title || ''}
          </Text>

          <View
            style={[
              styles.row,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image
              source={icons.calendarWhite}
              style={{height: fp(2), width: fp(2), tintColor: colors.black}}
              resizeMode="contain"
            />
            <Text style={styles.innerTxt}>
              {t('date')}:{' '}
              {moment(data?.date || item?.date).format('DD MMM YYYY')}
            </Text>
          </View>

          <View
            style={[
              styles.row,
              {flexDirection: isRTL ? 'row-reverse' : 'row'},
            ]}>
            <Image
              source={icons.clockYellow}
              style={{height: fp(2), width: fp(2), tintColor: colors.black}}
              resizeMode="contain"
            />
            <Text style={styles.innerTxt}>
              {t('time')}:{' '}
              <Text style={styles.innerTxt}>
                {convertToLocal12HourFormat(
                  data?.start_time || item?.tlm_tutor_schedule?.start_time,
                )}{' '}
                -{' '}
                {convertToLocal12HourFormat(
                  data?.end_time || item?.tlm_tutor_schedule?.end_time,
                )}
                {/* {`${moment(
                data?.start_time || item?.tlm_tutor_schedule?.start_time,
                'HH:mm:ss',
              ).format('hh:mm A')} - ${moment(
                data?.end_time || item?.tlm_tutor_schedule?.end_time,
                'HH:mm:ss',
              ).format('hh:mm A')}`} */}
              </Text>
            </Text>
          </View>
        </LinearGradient>

        {/* <View style={styles.card}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            {user_type == '1' ? (
              <TutorDetails
                imageUri={
                  data?.tlm_booking?.tutor?.image
                    ? {uri: IMAGE_BASE_URL + data?.tlm_booking?.tutor?.image}
                    : {uri: IMAGE_BASE_URL + DUMMY_USER_IMG}
                }
                name={data?.tlm_booking?.tutor?.name}
                occupation={
                  data?.tlm_booking?.tutor?.tlm_tutor_profile?.expertise
                }
              />
            ) : ( */}
        {/* <TutorDetails
                imageUri={
                  data?.tlm_booking?.tlm_booking_enrollments[0]?.tlm_user?.image
                    ? {
                        uri:
                          IMAGE_BASE_URL +
                          data?.tlm_booking?.tlm_booking_enrollments[0]
                            ?.tlm_user?.image,
                      }
                    : {uri: IMAGE_BASE_URL + DUMMY_USER_IMG}
                }
                name={
                  data?.tlm_booking?.tlm_booking_enrollments[0]?.tlm_user?.name
                }
                occupation={data?.tlm_booking?.class_title}
              />
            )} */}
        {/* <TouchableOpacity style={styles.messageBtn}>
              <Image source={icons.messageIcon} />
            </TouchableOpacity> */}
        {/* </View>
        </View> */}

        {user_type == '3' && (
          <PrimaryButton
            title={t('cancelBooking')}
            // onPress={() => navigation.navigate('StartClassScreen')}
            style={{
              backgroundColor: 'transparent',
              borderColor: 'grey',
              borderWidth: 0.8,
              // width: wp(100),
            }}
            textStyle={{color: 'grey'}}
            onPress={() => setIsLogoutModalVisible(true)}
          />
        )}

        {scheduleDetailsData?.data?.bookingSchedule?.status == '2' &&
          user_type == '1' && (
            <View style={{marginHorizontal: wp(3), marginVertical: hp(2)}}>
              <Text style={styles.blackTxt}>{t('bookingCancel')}</Text>
              <Text
                style={{
                  color: colors.red,
                  fontFamily: Fonts.medium,
                  marginTop: hp(1),
                }}>
                {
                  scheduleDetailsData?.data?.bookingSchedule
                    ?.cancellation_reason
                }
              </Text>
            </View>
          )}
      </ScrollView>
      {/* //Student */}

      {/* <View style={{marginBottom: hp(4)}}>
        <PrimaryButton
          title={t('Join Class')}
          onPress={handleJoinClassStudent}
        />
      </View> */}

      {type == 'openSession' && user_type == '1' ? (
        <View style={{marginBottom: hp(4)}}>
          <PrimaryButton
            title={t('Join Class')}
            onPress={handleJoinClassStudent}
          />
        </View>
      ) : (
        scheduleDetailsData?.data?.bookingSchedule?.status == '1' &&
        user_type == '1' && (
          <View>
            <PrimaryButton
              title={t('Join Class')}
              onPress={handleJoinClassStudent}
            />
          </View>
        )
      )}

      {/* {scheduleDetailsData?.data?.bookingSchedule?.status == '1' &&
        user_type == '1' && (
        
        )} */}

      {scheduleDetailsData?.data?.bookingSchedule?.status == '2' &&
        user_type == '1' && (
          <PrimaryButton
            title={t('reschedule')}
            onPress={() => {
              dispatch(updateSlot([]));
              dispatch(updateGroupStudents([]));
              dispatch(updatePackage({}));
              dispatch(updateClassType({}));
              dispatch(resetSelectedSlots());
              dispatch(updateSelectedSlots({}));
              dispatch(updateMeetingTutorPoint({}));

              navigation.navigate('RebookTutor', {
                scheduleData: scheduleDetailsData?.data,
                tutor,
                bookingDetails,
              });
            }}
          />
        )}
      {/* //Tutor */}
      {user_type == '3' && (
        <View style={{marginBottom: hp(4)}}>
          <PrimaryButton
            title={t('start_class')}
            onPress={handleStartClassTutor}
            // onPress={() => navigation.navigate('VideoTest')}
          />
        </View>
      )}

      <Modal
        animationType="fade"
        transparent={true}
        visible={isLogoutModalVisible}
        onRequestClose={handleCancelLogout}>
        <View style={logoutModalStyles.centeredView}>
          <View style={logoutModalStyles.modalView}>
            <Text style={logoutModalStyles.modalTitle}>
              {t('confirmCancel')}
            </Text>

            <Text style={logoutModalStyles.modalText}>{t('reason')}</Text>
            <TextInput
              style={logoutModalStyles.input}
              placeholder={t('typeHere')}
              value={reason}
              onChangeText={newText => setReason(newText)}
            />

            <View style={logoutModalStyles.buttonContainer}>
              <TouchableOpacity
                style={[
                  logoutModalStyles.button,
                  logoutModalStyles.cancelButton,
                ]}
                onPress={handleCancelLogout}>
                <Text style={logoutModalStyles.cancelButtonText}>
                  {t('close')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  logoutModalStyles.button,
                  logoutModalStyles.logoutButton,
                ]}
                onPress={handleCancelTutorBooking}>
                <Text style={logoutModalStyles.logoutButtonText}>
                  {t('cancelBooking')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

// Additional styles for the logout modal
const logoutModalStyles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    // alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    // alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    marginBottom: 15,
    // textAlign: 'center',
    fontSize: 18,
    fontFamily: Fonts.bold,
    color: colors.black,
  },
  modalText: {
    marginBottom: 8,
    fontFamily: Fonts.medium,
    color: colors.black,
    fontSize: fp(1.6),
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: hp(2),
  },
  button: {
    borderRadius: 10,
    padding: 10,
    elevation: 2,
    marginHorizontal: 10,
    minWidth: 100,
  },
  cancelButton: {
    backgroundColor: colors.txtGrey,
  },
  logoutButton: {
    backgroundColor: colors.themeColor,
  },
  cancelButtonText: {
    color: 'black',
    fontFamily: Fonts.bold,
    fontSize: responsiveFontSize(14),
    textAlign: 'center',
  },
  logoutButtonText: {
    color: 'white',
    fontFamily: Fonts.bold,
    fontSize: responsiveFontSize(14),
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: 'grey',
    borderWidth: 2,
    width: wp(70),
    paddingHorizontal: 8,
    marginBottom: 16,
    borderRadius: fp(1),
    fontFamily: Fonts.medium,
  },
  text: {
    fontSize: 16,
    fontFamily: Fonts.medium,
  },
});

export default ClassDetailsTwo;
