import messaging from '@react-native-firebase/messaging';
import notifee, {AndroidImportance} from '@notifee/react-native';
import {Platform} from 'react-native';
import {store} from '../../Store/Store'; // Ensure this is the correct store import
import apiSlice from '../../Api/ApiSlice';
import {
  setUnreadCount,
  setUnreadMessageCount,
  setUnreadSupportMessageCount,
} from '../../Redux/Slices/NotiSlice/NotificationSlice';

function onMessageReceived(message) {
  console.log('🚀 ~ onMessageReceived ~ message:', message);

  if (message) {
    console.log('🚀:', message);

    // Display the notification using notifee
    if (Platform.OS === 'ios') {
      notifee.displayNotification(message.notification);
    } else {
      notifee.displayNotification({
        title: message?.data?.title,
        body: message?.data?.body,
        android: {
          channelId: 'channel_id',
          importance: AndroidImportance.HIGH,
        },
      });
    }

    // Fetch and update unread counts
    store
      .dispatch(
        apiSlice.endpoints.getUnreadNotiCount.initiate(undefined, {
          forceRefetch: true,
        }),
      )
      .unwrap()
      .then(data => {
        const {unreadNotificationCount, unreadChatCount, unreadSuportMessages} =
          data.data;
        store.dispatch(setUnreadCount(unreadNotificationCount || 0));
        store.dispatch(setUnreadMessageCount(unreadChatCount || 0));
        store.dispatch(setUnreadSupportMessageCount(unreadSuportMessages || 0));
      })
      .catch(err => {
        console.error('Error fetching unread counts:', err);
      });
  }
}

export const notificationListener = async () => {
  // Request notification permissions

  messaging().getInitialNotification().then(onMessageReceived);
  messaging().onMessage(onMessageReceived);
  messaging().onNotificationOpenedApp(onMessageReceived);
  messaging().setBackgroundMessageHandler(async message => {
    console.log('Background Notification:', message);
    onMessageReceived(message);
  });
};
